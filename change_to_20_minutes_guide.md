# 将烟气监控从10分钟改为20分钟数据采集指南

## 概述

当前系统配置为采集10分钟的数据（200个数据点，每3秒一个点）。要改为20分钟，需要修改以下几个关键位置：

## 核心参数计算

- **当前配置**: 10分钟 = 600秒 ÷ 3秒/点 = 200个数据点
- **目标配置**: 20分钟 = 1200秒 ÷ 3秒/点 = 400个数据点

## 需要修改的文件和位置

### 1. monitoring_datasource.h
**位置**: 第122行
```cpp
// 当前
static const int MAX_DATA_POINTS = 200; // 10分钟按照3秒采集的频率那就是200个点。

// 修改为
static const int MAX_DATA_POINTS = 400; // 20分钟按照3秒采集的频率那就是400个点。
```

### 2. monitoring_datasource.cpp

#### 2.1 getMaxWindowDataPoints函数 (第202行)
```cpp
// 当前
int windowSeconds = 10 * 60;  // 10分钟 = 600秒

// 修改为
int windowSeconds = 20 * 60;  // 20分钟 = 1200秒
```

#### 2.2 smokeO2Data函数注释 (第295行)
```cpp
// 当前
// 固定3秒采集间隔，200个点=10分钟

// 修改为
// 固定3秒采集间隔，400个点=20分钟
```

#### 2.3 smokeCOData函数注释 (第320行)
```cpp
// 当前
// 固定3秒采集间隔，200个点=10分钟

// 修改为
// 固定3秒采集间隔，400个点=20分钟
```

#### 2.4 addDataPoint函数注释 (第383行)
```cpp
// 当前
// 简单逻辑：MAX_DATA_POINTS=200就是10分钟的数据

// 修改为
// 简单逻辑：MAX_DATA_POINTS=400就是20分钟的数据
```

#### 2.5 addDataPoint函数注释 (第385行)
```cpp
// 当前
m_dataCount++;  // 还没满10分钟，继续增加

// 修改为
m_dataCount++;  // 还没满20分钟，继续增加
```

#### 2.6 addDataPoint函数注释 (第387行)
```cpp
// 当前
// 满了200个点后，m_dataCount保持200，实现滑动窗口

// 修改为
// 满了400个点后，m_dataCount保持400，实现滑动窗口
```

#### 2.7 updateSmokeChartSeriesWithMinutes函数注释 (第700行)
```cpp
// 当前
// 固定3秒采集间隔，200个点=10分钟

// 修改为
// 固定3秒采集间隔，400个点=20分钟
```

#### 2.8 updateSmokeChartSeriesWithMinutes函数注释 (第703行)
```cpp
// 当前
// 构建数据点列表 - 将最近的数据映射到0-10分钟

// 修改为
// 构建数据点列表 - 将最近的数据映射到0-20分钟
```

#### 2.9 updateChartIncremental函数注释 (第886行)
```cpp
// 当前
// 如果缓冲区已满（达到200个点），总是使用全量更新来实现滑动窗口

// 修改为
// 如果缓冲区已满（达到400个点），总是使用全量更新来实现滑动窗口
```

#### 2.10 updateChartIncremental函数注释 (第898行)
```cpp
// 当前
// 对于未满200个点的情况，使用增量更新

// 修改为
// 对于未满400个点的情况，使用增量更新
```

### 3. monitoring_datasource.h

#### 3.1 getMaxWindowDataPoints函数注释 (第64行)
```cpp
// 当前
// 获取10分钟窗口需要的最大数据点数

// 修改为
// 获取20分钟窗口需要的最大数据点数
```

#### 3.2 updateSmokeChartSeriesWithMinutes函数注释 (第74行)
```cpp
// 当前
// 简化的图表更新方法 - 只支持10分钟视图

// 修改为
// 简化的图表更新方法 - 只支持20分钟视图
```

#### 3.3 DataPoint结构体注释 (第119行)
```cpp
// 当前
// 采样间隔：3秒/点，由前端使用 index * 3000ms 计算时间

// 修改为
// 采样间隔：3秒/点，由前端使用 index * 3000ms 计算时间
```

### 4. MonitoringSystem.qml

#### 4.1 注释修改 (第94行和第106行)
```qml
// 当前
// X轴固定0-10分钟，不需要更新范围

// 修改为
// X轴固定0-20分钟，不需要更新范围
```

#### 4.2 属性定义注释 (第739行)
```qml
// 当前
// 属性定义 - 简化为只有10分钟时间区间

// 修改为
// 属性定义 - 简化为只有20分钟时间区间
```

#### 4.3 zoomLevels和zoomLabels (第742-743行)
```qml
// 当前
property var zoomLevels: [1.0]  // 只保留10分钟区间
property var zoomLabels: ["10分钟"]

// 修改为
property var zoomLevels: [1.0]  // 只保留20分钟区间
property var zoomLabels: ["20分钟"]
```

#### 4.4 currentZoomIndex注释 (第744行)
```qml
// 当前
property int currentZoomIndex: 0  // 当前缩放级别索引，默认选中10分钟

// 修改为
property int currentZoomIndex: 0  // 当前缩放级别索引，默认选中20分钟
```

#### 4.5 windowSize属性 (第765行)
```qml
// 当前
property double windowSize: 10.0  // 10分钟窗口

// 修改为
property double windowSize: 20.0  // 20分钟窗口
```

#### 4.6 X轴范围注释 (第771行)
```qml
// 当前
// 固定X轴范围 - 始终显示0-10分钟

// 修改为
// 固定X轴范围 - 始终显示0-20分钟
```

#### 4.7 tickCount调整 (第767行)
```qml
// 当前
tickCount: 6  // 每2分钟一个刻度

// 修改为
tickCount: 11  // 每2分钟一个刻度 (0,2,4,6,8,10,12,14,16,18,20)
```

#### 4.8 滚动步长注释 (第872行)
```qml
// 当前
// 10分钟视图：0.5分钟步长

// 修改为
// 20分钟视图：0.5分钟步长
```

#### 4.9 useMinutes注释 (第949行)
```qml
// 当前
var useMinutes = true // 10分钟视图始终使用分钟

// 修改为
var useMinutes = true // 20分钟视图始终使用分钟
```

#### 4.10 Component.onCompleted注释 (第1026行)
```qml
// 当前
// 初始化时使用全量更新 - 简化为10分钟视图

// 修改为
// 初始化时使用全量更新 - 简化为20分钟视图
```

#### 4.11 缩放控制按钮注释 (第1318行)
```qml
// 当前
// 缩放控制按钮 - 已简化为只有10分钟，注释掉按钮选择区域

// 修改为
// 缩放控制按钮 - 已简化为只有20分钟，注释掉按钮选择区域
```

#### 4.12 简化版本注释 (第1386行)
```qml
// 当前
// 简化版本：只显示当前10分钟视图信息

// 修改为
// 简化版本：只显示当前20分钟视图信息
```

#### 4.13 时间范围显示 (第1393行)
```qml
// 当前
text: "时间范围: 10分钟"

// 修改为
text: "时间范围: 20分钟"
```

## 修改步骤建议

1. **先备份当前代码**
2. **按顺序修改文件**：
   - 先修改 `monitoring_datasource.h` 中的 `MAX_DATA_POINTS`
   - 再修改 `monitoring_datasource.cpp` 中的所有相关位置
   - 最后修改 `MonitoringSystem.qml` 中的UI相关配置
3. **编译测试**
4. **验证功能**：
   - 检查数据采集是否正常
   - 验证20分钟后滑动窗口是否正常工作
   - 确认X轴显示范围为0-20分钟

## 注意事项

- 修改后内存使用会增加一倍（从200个点增加到400个点）
- 图表渲染可能会稍微慢一些，但应该不会有明显影响
- 确保所有注释和变量名都保持一致性
- 测试时需要等待20分钟才能看到完整的滑动窗口效果

## 验证清单

- [x] MAX_DATA_POINTS 改为 400 ✅
- [x] 所有"10分钟"注释改为"20分钟" ✅
- [x] 所有"200个点"注释改为"400个点" ✅
- [x] windowSize 改为 20.0 ✅
- [x] tickCount 改为 11 ✅
- [x] zoomLabels 改为 ["20分钟"] ✅
- [x] 时间范围显示改为 "时间范围: 20分钟" ✅
- [x] 修复注释中的"600秒"为"1200秒" ✅
- [ ] 编译成功
- [ ] 功能测试通过
